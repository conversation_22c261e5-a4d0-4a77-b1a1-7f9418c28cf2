<?php
/**
 * Admin controller pro správu technologií - OPRAVENÁ VERZE
 * Problém: Nespráv<PERSON> detekce submit tlačítka
 */

// Načtení autoloaderu modulu
require_once _PS_MODULE_DIR_ . 'technologie/technologie.php';

use PrestaShop\Module\Technologie\Entity\Technologie as TechnologieEntity;
use PrestaShop\Module\Technologie\Repository\TechnologieRepository;
use PrestaShop\Module\Technologie\Repository\TechnologieDbRepository;
use PrestaShop\Module\Technologie\Form\TechnologieType;
use PrestaShop\Module\Technologie\Form\BulkActionType;
use PrestaShop\Module\Technologie\Form\FileUploadHandler;

class AdminTechnologieController extends ModuleAdminController
{
    private $technologieRepository = null;
    private $fileUploadHandler = null;

    public function __construct($bootstrap = true, $display = true)
    {
        $this->bootstrap = true;
        $this->table = 'technologie';
        $this->className = 'TechnologieModel';
        $this->lang = false;
        $this->addRowAction('edit');
        $this->addRowAction('delete');

        parent::__construct($bootstrap, $display);

        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Smazat vybrané'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Opravdu chcete smazat vybrané technologie?')
            ],
            'enableSelection' => [
                'text' => $this->l('Aktivovat vybrané'),
                'icon' => 'icon-power-off text-success'
            ],
            'disableSelection' => [
                'text' => $this->l('Deaktivovat vybrané'),
                'icon' => 'icon-power-off text-danger'
            ]
        ];
    }

    /**
     * Inicializace controlleru
     */
    public function init()
    {
        parent::init();

        // Inicializace ID objektu
        $this->id_object = (int)Tools::getValue('id_' . $this->table);
    }

    /**
     * Inicializace obsahu - rozhoduje mezi seznamem a formulářem
     */
    public function initContent()
    {
        // Zpracování AJAX požadavků - rozšířená detekce
        $isAjax = Tools::isSubmit('ajax') ||
                  (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') ||
                  Tools::getValue('ajax') == '1';

        if ($isAjax) {
            $action = Tools::getValue('action');
            error_log("AJAX request detected. Action: " . $action);
            error_log("POST data: " . print_r($_POST, true));

            if ($action === 'updatePositions') {
                $this->ajaxProcessUpdatePositions();
                return;
            } elseif ($action === 'toggleStatus') {
                $this->ajaxProcessToggleStatus();
                return;
            }
        }

        // Rozhodnutí mezi seznamem a formulářem
        $showForm = false;

        // Kontrola různých způsobů volání formuláře
        if (Tools::isSubmit('add' . $this->table) || Tools::getValue('add' . $this->table)) {
            $showForm = true;
        } elseif (Tools::isSubmit('update' . $this->table) || Tools::getValue('update' . $this->table)) {
            $showForm = true;
        } elseif ($this->id_object > 0) {
            $showForm = true;
        }

        if ($showForm) {
            $this->content = $this->renderForm();
        } else {
            $this->content = $this->renderList();
        }

        $this->context->smarty->assign([
            'content' => $this->content,
            'url_post' => self::$currentIndex . '&token=' . $this->token,
            'show_page_header_toolbar' => $this->show_page_header_toolbar,
            'page_header_toolbar_title' => $this->page_header_toolbar_title,
            'page_header_toolbar_btn' => $this->page_header_toolbar_btn
        ]);
    }

    /**
     * Zpracování POST akcí
     */
    public function postProcess()
    {
        // Zpracování AJAX požadavků také zde (fallback)
        $isAjax = Tools::isSubmit('ajax') ||
                  (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') ||
                  Tools::getValue('ajax') == '1';

        if ($isAjax) {
            $action = Tools::getValue('action');
            if ($action === 'updatePositions') {
                $this->ajaxProcessUpdatePositions();
                return;
            } elseif ($action === 'toggleStatus') {
                $this->ajaxProcessToggleStatus();
                return;
            }
        }

        // Zpracování mazání
        if (Tools::isSubmit('delete' . $this->table)) {
            $this->processDelete();
        }

        // Zpracování hromadných akcí
        if (Tools::isSubmit('submitBulkdelete' . $this->table)) {
            $this->processBulkDelete();
        }

        if (Tools::isSubmit('submitBulkenableSelection' . $this->table)) {
            $this->processBulkEnableSelection();
        }

        if (Tools::isSubmit('submitBulkdisableSelection' . $this->table)) {
            $this->processBulkDisableSelection();
        }

        parent::postProcess();
    }

    private function getTechnologieRepository()
    {
        if ($this->technologieRepository === null) {
            try {
                if (method_exists($this, 'get') && $this->get('doctrine.orm.entity_manager')) {
                    $entityManager = $this->get('doctrine.orm.entity_manager');
                    $this->technologieRepository = $entityManager->getRepository(TechnologieEntity::class);
                } else {
                    throw new \Exception('Doctrine není dostupné');
                }
            } catch (\Exception $e) {
                $this->technologieRepository = new TechnologieDbRepository();
            }
        }
        return $this->technologieRepository;
    }

    private function getFileUploadHandler()
    {
        if ($this->fileUploadHandler === null) {
            $this->fileUploadHandler = new FileUploadHandler();
        }
        return $this->fileUploadHandler;
    }

    private function ensureUploadDirectory(): ?string
    {
        $uploadDir = _PS_MODULE_DIR_ . 'technologie/uploads/';

        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                $this->errors[] = $this->l('Nelze vytvořit upload adresář');
                return null;
            }
        }

        if (!is_writable($uploadDir)) {
            $this->errors[] = $this->l('Upload adresář není zapisovatelný');
            return null;
        }

        $htaccessPath = $uploadDir . '.htaccess';
        if (!file_exists($htaccessPath)) {
            $htaccessContent = "Options -Indexes\n";
            $htaccessContent .= "RedirectMatch 403 \.php$\n";
            file_put_contents($htaccessPath, $htaccessContent);
        }

        return $uploadDir;
    }

    private function validateUploadedFile(array $file): bool
    {
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 2 * 1024 * 1024; // 2MB

        if ($file['size'] > $maxSize) {
            $this->errors[] = $this->l('Soubor je příliš velký. Maximální velikost je 2MB');
            return false;
        }

        if (function_exists('finfo_open')) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $file['tmp_name']);
            finfo_close($finfo);

            if (!in_array($mimeType, $allowedTypes)) {
                $this->errors[] = $this->l('Nepovolený typ souboru. Povolené: JPG, PNG, GIF, WebP');
                return false;
            }
        }

        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            $this->errors[] = $this->l('Soubor není platný obrázek');
            return false;
        }

        return true;
    }

    private function generateSecureFilename(array $file): string
    {
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (empty($extension)) {
            $mimeExtensions = [
                'image/jpeg' => 'jpg',
                'image/png' => 'png', 
                'image/gif' => 'gif',
                'image/webp' => 'webp'
            ];
            
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $file['tmp_name']);
            finfo_close($finfo);
            
            $extension = $mimeExtensions[$mimeType] ?? 'jpg';
        }

        return 'tech_' . time() . '_' . uniqid() . '.' . $extension;
    }

    private function handleImageUpload(array $file)
    {
        try {
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $this->errors[] = $this->l('Chyba při nahrávání souboru');
                return null;
            }

            if (!file_exists($file['tmp_name']) || !is_readable($file['tmp_name'])) {
                $this->errors[] = $this->l('Dočasný soubor není dostupný');
                return null;
            }

            if (!$this->validateUploadedFile($file)) {
                return null;
            }

            $uploadDir = $this->ensureUploadDirectory();
            if (!$uploadDir) {
                return null;
            }

            $filename = $this->generateSecureFilename($file);
            $targetPath = $uploadDir . $filename;

            if (move_uploaded_file($file['tmp_name'], $targetPath)) {
                chmod($targetPath, 0644);

                if (file_exists($targetPath) && is_readable($targetPath)) {
                    return $filename;
                } else {
                    $this->errors[] = $this->l('Soubor byl nahrán, ale není přístupný');
                    return null;
                }
            } else {
                $this->errors[] = $this->l('Nepodařilo se přesunout nahraný soubor');
                return null;
            }

        } catch (\Exception $e) {
            $this->errors[] = $this->l('Chyba při zpracování obrázku: ') . $e->getMessage();
            return null;
        }
    }

    /**
     * OPRAVENÁ VERZE - Lepší detekce submit tlačítka
     */
    private function processFormSubmission($technologie = null)
    {
        // Detekce submit tlačítka
        $isSubmitted = false;

        // Kontrola všech možných variant submit klíčů
        $possibleSubmitKeys = [
            'submitAddtechnologie',
            'submitAdd' . $this->table,
            'submitAdd',
            'submit'
        ];

        foreach ($possibleSubmitKeys as $key) {
            if (isset($_POST[$key])) {
                $isSubmitted = true;
                break;
            }
        }

        // Také kontrola všech POST klíčů obsahujících 'submit'
        if (!$isSubmitted) {
            foreach ($_POST as $key => $value) {
                if (strpos(strtolower($key), 'submit') !== false) {
                    $isSubmitted = true;
                    break;
                }
            }
        }

        // Pokud není detekován submit, zkusíme alternativní metodu
        if (!$isSubmitted && $_SERVER['REQUEST_METHOD'] === 'POST') {
            // Pokud je POST a obsahuje povinná pole, považujeme za submit
            if (isset($_POST['name']) && !empty($_POST['name'])) {
                $isSubmitted = true;
            }
        }

        // Pokud stále není detekován submit, ukončíme
        if (!$isSubmitted) {
            return false;
        }

        try {
            $name = Tools::getValue('name', '');
            $description = Tools::getValue('description', '');
            $slug = Tools::getValue('slug', '');
            $detailedDescription = Tools::getValue('detailed_description', '');
            $advantages = Tools::getValue('advantages', '');
            $applications = Tools::getValue('applications', '');
            $position = (int)Tools::getValue('position', 0);
            $active = Tools::getValue('active') ? true : false;

            if (empty($name)) {
                $this->errors[] = $this->l('Název technologie je povinný');
                return false;
            }

            // Generování slug pokud není zadán
            if (empty($slug)) {
                $slug = $this->generateSlug($name);
            } else {
                $slug = $this->sanitizeSlug($slug);
            }

            // Kontrola jedinečnosti slug
            if (!$this->isSlugUnique($slug, $this->id_object)) {
                $this->errors[] = $this->l('Tento slug již existuje. Zvolte jiný.');
                return false;
            }

            if ($this->id_object && $technologie && method_exists($technologie, 'setName')) {
                $editTechnologie = $technologie;
            } else {
                $editTechnologie = new TechnologieEntity();
            }

            $editTechnologie->setName($name);
            $editTechnologie->setDescription($description);
            $editTechnologie->setSlug($slug);
            $editTechnologie->setDetailedDescription($detailedDescription);
            $editTechnologie->setAdvantages($advantages);
            $editTechnologie->setApplications($applications);
            $editTechnologie->setActive($active);

            if ($position <= 0) {
                try {
                    $position = $this->getTechnologieRepository()->getMaxPosition() + 1;
                } catch (\Exception $e) {
                    $position = 1;
                }
            }
            $editTechnologie->setPosition($position);

            // Zpracování obrázku
            $currentImage = '';
            if ($this->id_object && method_exists($editTechnologie, 'getImage')) {
                $currentImage = $editTechnologie->getImage() ?? '';
            }

            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $newFilename = $this->handleImageUpload($_FILES['image']);

                if ($newFilename) {
                    if ($currentImage && $currentImage !== $newFilename) {
                        $this->deleteImageFile($currentImage);
                    }

                    $editTechnologie->setImage($newFilename);
                } else {
                    if ($currentImage) {
                        $editTechnologie->setImage($currentImage);
                    }
                    return false;
                }
            } else {
                if ($currentImage) {
                    $editTechnologie->setImage($currentImage);
                }
            }

            // Zpracování galerie obrázků
            $this->processGalleryImages($editTechnologie);

            $this->getTechnologieRepository()->save($editTechnologie);
            return true;

        } catch (\Exception $e) {
            $this->errors[] = $this->l('Chyba při ukládání: ') . $e->getMessage();
            return false;
        }
}

    private function deleteImageFile(string $filename): bool
    {
        if (empty($filename)) {
            return true;
        }

        $uploadDir = _PS_MODULE_DIR_ . 'technologie/uploads/';
        $filePath = $uploadDir . $filename;
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return true;
    }

    private function prepareTechnologieData($technologie): array
    {
        if ($this->id_object && $technologie && method_exists($technologie, 'getName')) {
            $imageUrl = '';
            if ($technologie->getImage()) {
                $imageUrl = _MODULE_DIR_ . 'technologie/uploads/' . $technologie->getImage();
            }

            return [
                'name' => $technologie->getName(),
                'description' => $technologie->getDescription(),
                'slug' => $technologie->getSlug(),
                'detailed_description' => $technologie->getDetailedDescription(),
                'advantages' => $technologie->getAdvantages(),
                'applications' => $technologie->getApplications(),
                'gallery_images' => $technologie->getGalleryImages(),
                'gallery_images_array' => $technologie->getGalleryImagesArray(),
                'image' => $technologie->getImage(),
                'position' => $technologie->getPosition(),
                'active' => $technologie->isActive() ? 1 : 0,
                'image_url' => $imageUrl
            ];
        } else {
            return [
                'name' => '',
                'description' => '',
                'slug' => '',
                'detailed_description' => '',
                'advantages' => '',
                'applications' => '',
                'gallery_images' => '',
                'gallery_images_array' => [],
                'image' => '',
                'position' => 0,
                'active' => 1,
                'image_url' => ''
            ];
        }
    }

    /**
     * Renderování formuláře pro přidání/editaci technologie
     */
    public function renderForm()
    {
        // Detekce POST požadavku
        $isPostRequest = ($_SERVER['REQUEST_METHOD'] === 'POST');

        // Načtení entity pro editaci
        $technologie = null;
        if ($this->id_object) {
            try {
                $technologie = $this->getTechnologieRepository()->findOneById((int)$this->id_object);
                if (!$technologie) {
                    $this->errors[] = $this->l('Technologie nebyla nalezena');
                    return $this->renderList();
                }
            } catch (\Exception $e) {
                $this->errors[] = $this->l('Chyba při načítání technologie: ') . $e->getMessage();
                return $this->renderList();
            }
        }

        // Zpracování POST požadavku
        if ($isPostRequest) {
            if ($this->processFormSubmission($technologie)) {
                $this->confirmations[] = $this->l('Technologie byla úspěšně uložena');

                if (empty($this->errors)) {
                    $adminLink = $this->context->link->getAdminLink('AdminTechnologie');
                    Tools::redirectAdmin($adminLink);
                }
            }
        }

        // Příprava dat pro šablonu
        $technologieData = $this->prepareTechnologieData($technologie);

        $this->context->smarty->assign([
            'technologie' => $technologieData,
            'is_edit' => (bool)$this->id_object,
            'table' => $this->table,
            'upload_dir' => _MODULE_DIR_ . 'technologie/uploads/',
            'back_url' => $this->context->link->getAdminLink('AdminTechnologie'),
            'errors' => $this->errors
        ]);

        return $this->context->smarty->fetch(_PS_MODULE_DIR_ . 'technologie/views/templates/admin/form.tpl');
    }

    // Zbytek metod zůstává stejný...
    public function renderList()
    {
        try {
            // Načtení všech technologií
            $technologie_list = $this->getTechnologieRepository()->findAllOrderedByPosition();

            // Příprava dat pro šablonu
            $technologie_data = [];
            foreach ($technologie_list as $tech) {
                $technologie_data[] = [
                    'id' => $tech->getId(),
                    'name' => $tech->getName(),
                    'description' => $tech->getDescription(),
                    'image' => $tech->getImage(),
                    'position' => $tech->getPosition(),
                    'active' => $tech->isActive(),
                    'edit_url' => $this->context->link->getAdminLink('AdminTechnologie') . '&id_technologie=' . $tech->getId() . '&updatetechnologie',
                    'delete_url' => $this->context->link->getAdminLink('AdminTechnologie') . '&id_technologie=' . $tech->getId() . '&deletetechnologie'
                ];
            }

            // Přiřazení proměnných do Smarty
            $this->context->smarty->assign([
                'technologie_list' => $technologie_data,
                'technologie_count' => count($technologie_data),
                'add_url' => $this->context->link->getAdminLink('AdminTechnologie') . '&addtechnologie',
                'upload_dir' => _MODULE_DIR_ . 'technologie/uploads/',
                'module_dir' => _MODULE_DIR_ . 'technologie/',
                'errors' => $this->errors,
                'confirmations' => $this->confirmations
            ]);

            // Načtení CSS a JS
            $this->addCSS(_MODULE_DIR_ . 'technologie/views/css/admin.css');
            $this->addJS(_MODULE_DIR_ . 'technologie/views/js/admin.js');

            return $this->context->smarty->fetch(_PS_MODULE_DIR_ . 'technologie/views/templates/admin/list.tpl');

        } catch (\Exception $e) {
            $this->errors[] = $this->l('Chyba při načítání seznamu technologií: ') . $e->getMessage();
            return '<div class="alert alert-danger">' . implode('<br>', $this->errors) . '</div>';
        }
    }

    public function getList($id_lang, $order_by = null, $order_way = null, $start = 0, $limit = null, $id_lang_shop = false)
    {
        try {
            $technologie = $this->getTechnologieRepository()->findAllOrderedByPosition();

            $list = [];
            foreach ($technologie as $tech) {
                $list[] = [
                    'id_technologie' => $tech->getId(),
                    'name' => $tech->getName(),
                    'description' => $tech->getDescription(),
                    'image' => $tech->getImage(),
                    'position' => $tech->getPosition(),
                    'active' => $tech->isActive(),
                    'date_add' => $tech->getDateAdd()->format('Y-m-d H:i:s')
                ];
            }

            $this->_list = $list;
            $this->_listTotal = count($list);

            return $list;

        } catch (\Exception $e) {
            return $this->getListFromDatabase($id_lang, $order_by, $order_way, $start, $limit);
        }
    }

    private function getListFromDatabase($id_lang, $order_by = null, $order_way = null, $start = 0, $limit = null)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'technologie` ORDER BY position ASC, name ASC';

        if ($limit) {
            $sql .= ' LIMIT ' . (int)$start . ', ' . (int)$limit;
        }

        $results = Db::getInstance()->executeS($sql);

        if (!$results) {
            $results = [];
        }

        $formattedResults = [];
        foreach ($results as $row) {
            $formattedResults[] = [
                'id_technologie' => $row['id_technologie'],
                'name' => $row['name'],
                'description' => $row['description'],
                'image' => $row['image'],
                'position' => $row['position'],
                'active' => $row['active'],
                'date_add' => $row['date_add']
            ];
        }

        $this->_list = $formattedResults;
        $this->_listTotal = count($formattedResults);

        return $formattedResults;
    }

    public function displayImage($value, $row)
    {
        if (empty($value)) {
            return '<span class="text-muted">Bez obrázku</span>';
        }

        $imageUrl = _MODULE_DIR_ . 'technologie/uploads/' . $value;
        return '<img src="' . $imageUrl . '" alt="' . htmlspecialchars($row['name']) . '"
                style="max-width: 50px; max-height: 50px; object-fit: cover;"
                class="img-thumbnail">';
    }

    // Bulk action metody...
    public function processBulkDelete()
    {
        $ids = Tools::getValue('technologieBox');
        if (!is_array($ids) || empty($ids)) {
            $this->errors[] = $this->l('Nevybrali jste žádné technologie');
            return;
        }

        try {
            foreach ($ids as $id) {
                $technologie = $this->getTechnologieRepository()->findOneById((int)$id);
                if ($technologie) {
                    if ($technologie->getImage()) {
                        $this->deleteImageFile($technologie->getImage());
                    }
                    $this->getTechnologieRepository()->delete($technologie);
                }
            }
            $this->confirmations[] = sprintf($this->l('Bylo smazáno %d technologií'), count($ids));
        } catch (\Exception $e) {
            $this->errors[] = $this->l('Chyba při mazání: ') . $e->getMessage();
        }
    }

    public function processBulkEnableSelection()
    {
        $this->processBulkStatus(true);
    }

    public function processBulkDisableSelection()
    {
        $this->processBulkStatus(false);
    }

    private function processBulkStatus($status)
    {
        $ids = Tools::getValue('technologieBox');
        if (!is_array($ids) || empty($ids)) {
            $this->errors[] = $this->l('Nevybrali jste žádné technologie');
            return;
        }

        try {
            $this->getTechnologieRepository()->bulkUpdateActive($ids, $status);
            $action = $status ? $this->l('aktivováno') : $this->l('deaktivováno');
            $this->confirmations[] = sprintf($this->l('Bylo %s %d technologií'), $action, count($ids));
        } catch (\Exception $e) {
            $this->errors[] = $this->l('Chyba při změně stavu: ') . $e->getMessage();
        }
    }

    public function processDelete()
    {
        if (!$this->id_object) {
            $this->errors[] = $this->l('Neplatné ID technologie');
            return;
        }

        try {
            $technologie = $this->getTechnologieRepository()->findOneById((int)$this->id_object);
            if (!$technologie) {
                $this->errors[] = $this->l('Technologie nebyla nalezena');
                return;
            }

            if ($technologie->getImage()) {
                $this->deleteImageFile($technologie->getImage());
            }

            $this->getTechnologieRepository()->delete($technologie);
            $this->confirmations[] = $this->l('Technologie byla úspěšně smazána');

        } catch (\Exception $e) {
            $this->errors[] = $this->l('Chyba při mazání: ') . $e->getMessage();
        }
    }

    public function ajaxProcessUpdatePositions()
    {
        error_log("ajaxProcessUpdatePositions called");
        error_log("POST data: " . print_r($_POST, true));

        if (!Tools::isSubmit('positions')) {
            error_log("Missing positions data");
            die(json_encode(['success' => false, 'message' => $this->l('Chybí data pozic')]));
        }

        try {
            $positions = json_decode(Tools::getValue('positions'), true);
            error_log("Decoded positions: " . print_r($positions, true));

            if (!$positions || !is_array($positions)) {
                throw new \Exception('Neplatná data pozic');
            }

            $this->getTechnologieRepository()->updatePositions($positions);
            error_log("Positions updated successfully");
            die(json_encode(['success' => true, 'message' => $this->l('Pořadí bylo aktualizováno')]));
        } catch (\Exception $e) {
            error_log("Error updating positions: " . $e->getMessage());
            die(json_encode(['success' => false, 'message' => $e->getMessage()]));
        }
    }

    public function ajaxProcessToggleStatus()
    {
        error_log("ajaxProcessToggleStatus called");
        error_log("POST data: " . print_r($_POST, true));

        if (!Tools::isSubmit('id') || !Tools::isSubmit('status')) {
            error_log("Missing id or status data");
            die(json_encode(['success' => false, 'message' => $this->l('Chybí data pro změnu stavu')]));
        }

        try {
            $id = (int)Tools::getValue('id');
            $status = (bool)Tools::getValue('status');

            error_log("Processing toggle for ID: $id, status: " . ($status ? 'true' : 'false'));

            $technologie = $this->getTechnologieRepository()->findOneById($id);
            if (!$technologie) {
                error_log("Technology not found for ID: $id");
                die(json_encode(['success' => false, 'message' => $this->l('Technologie nebyla nalezena')]));
            }

            $technologie->setActive($status);
            $this->getTechnologieRepository()->save($technologie);

            error_log("Status updated successfully for ID: $id");

            $statusText = $status ? $this->l('aktivována') : $this->l('deaktivována');
            die(json_encode(['success' => true, 'message' => sprintf($this->l('Technologie byla %s'), $statusText)]));
        } catch (\Exception $e) {
            error_log("Error toggling status: " . $e->getMessage());
            die(json_encode(['success' => false, 'message' => $e->getMessage()]));
        }
    }

    /**
     * Generování slug z názvu
     */
    private function generateSlug(string $text): string
    {
        $slug = strtolower($text);

        // Převod českých znaků
        $czech = ['á', 'à', 'â', 'ä', 'ã', 'é', 'è', 'ê', 'ë', 'í', 'ì', 'î', 'ï', 'ó', 'ò', 'ô', 'ö', 'õ', 'ú', 'ù', 'û', 'ü', 'ý', 'ÿ', 'ň', 'ř', 'š', 'č', 'ť', 'ž', 'ď', 'ů', 'ě'];
        $latin = ['a', 'a', 'a', 'a', 'a', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i', 'o', 'o', 'o', 'o', 'o', 'u', 'u', 'u', 'u', 'y', 'y', 'n', 'r', 's', 'c', 't', 'z', 'd', 'u', 'e'];
        $slug = str_replace($czech, $latin, $slug);

        // Odstranění nepovolených znaků
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);

        // Nahrazení mezer pomlčkami
        $slug = preg_replace('/\s+/', '-', $slug);

        // Odstranění vícenásobných pomlček
        $slug = preg_replace('/-+/', '-', $slug);

        // Odstranění pomlček na začátku a konci
        $slug = trim($slug, '-');

        return $slug;
    }

    /**
     * Sanitizace slug
     */
    private function sanitizeSlug(string $slug): string
    {
        $slug = strtolower($slug);
        $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');

        return $slug;
    }

    /**
     * Kontrola jedinečnosti slug
     */
    private function isSlugUnique(string $slug, ?int $excludeId = null): bool
    {
        try {
            return $this->getTechnologieRepository()->slugExists($slug, $excludeId) === false;
        } catch (\Exception $e) {
            // Fallback na databázový dotaz
            $sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'technologie` WHERE `slug` = "' . pSQL($slug) . '"';
            if ($excludeId) {
                $sql .= ' AND `id_technologie` != ' . (int)$excludeId;
            }

            $count = (int)Db::getInstance()->getValue($sql);
            return $count === 0;
        }
    }

    /**
     * Zpracování galerie obrázků
     */
    private function processGalleryImages($technologie): void
    {
        // Zpracování smazaných obrázků
        $removedImages = Tools::getValue('removed_gallery_images', '');
        if (!empty($removedImages)) {
            $removedImagesArray = explode(',', $removedImages);
            $currentGallery = $technologie->getGalleryImagesArray();

            foreach ($removedImagesArray as $removedImage) {
                $removedImage = trim($removedImage);
                if (!empty($removedImage)) {
                    // Smazání souboru
                    $this->deleteGalleryImage($removedImage);

                    // Odstranění z pole
                    $currentGallery = array_filter($currentGallery, function($img) use ($removedImage) {
                        return $img !== $removedImage;
                    });
                }
            }

            $technologie->setGalleryImagesArray(array_values($currentGallery));
        }

        // Zpracování nových obrázků
        if (isset($_FILES['gallery_images']) && is_array($_FILES['gallery_images']['name'])) {
            $currentGallery = $technologie->getGalleryImagesArray();

            for ($i = 0; $i < count($_FILES['gallery_images']['name']); $i++) {
                if ($_FILES['gallery_images']['error'][$i] === UPLOAD_ERR_OK) {
                    $file = [
                        'name' => $_FILES['gallery_images']['name'][$i],
                        'type' => $_FILES['gallery_images']['type'][$i],
                        'tmp_name' => $_FILES['gallery_images']['tmp_name'][$i],
                        'error' => $_FILES['gallery_images']['error'][$i],
                        'size' => $_FILES['gallery_images']['size'][$i]
                    ];

                    $newFilename = $this->handleGalleryImageUpload($file);
                    if ($newFilename) {
                        $currentGallery[] = $newFilename;
                    }
                }
            }

            $technologie->setGalleryImagesArray($currentGallery);
        }
    }

    /**
     * Upload obrázku do galerie
     */
    private function handleGalleryImageUpload(array $file): ?string
    {
        $uploadDir = _PS_MODULE_DIR_ . 'technologie/uploads/gallery/';

        // Vytvoření adresáře pokud neexistuje
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                $this->errors[] = $this->l('Nelze vytvořit adresář pro galerii');
                return null;
            }
        }

        // Validace souboru
        if (!$this->validateImageFile($file)) {
            return null;
        }

        // Generování jedinečného názvu
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $filename = 'gallery_' . time() . '_' . uniqid() . '.' . $extension;
        $targetPath = $uploadDir . $filename;

        // Upload souboru
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            return $filename;
        } else {
            $this->errors[] = $this->l('Chyba při uploadu obrázku do galerie');
            return null;
        }
    }

    /**
     * Smazání obrázku z galerie
     */
    private function deleteGalleryImage(string $filename): bool
    {
        if (empty($filename)) {
            return true;
        }

        $uploadDir = _PS_MODULE_DIR_ . 'technologie/uploads/gallery/';
        $filePath = $uploadDir . $filename;

        if (file_exists($filePath)) {
            return unlink($filePath);
        }

        return true;
    }

    /**
     * Validace obrázku
     */
    private function validateImageFile(array $file): bool
    {
        // Kontrola velikosti (2MB)
        if ($file['size'] > 2 * 1024 * 1024) {
            $this->errors[] = sprintf($this->l('Obrázek %s je příliš velký (max 2MB)'), $file['name']);
            return false;
        }

        // Kontrola MIME typu
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            $this->errors[] = sprintf($this->l('Neplatný typ souboru %s'), $file['name']);
            return false;
        }

        // Kontrola skutečného typu souboru
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            $this->errors[] = sprintf($this->l('Soubor %s není platný obrázek'), $file['name']);
            return false;
        }

        return true;
    }
}