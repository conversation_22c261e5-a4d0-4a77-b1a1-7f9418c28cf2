{**
 * Detail stránka technologie potisku
 * 
 * <AUTHOR> <<EMAIL>>
 * @version 1.3.0
 * @since 2024-12-19
 *}

{extends file='page.tpl'}

{block name='page_title'}
    {$technologie.name|escape:'html':'UTF-8'}
{/block}

{block name='page_header_container'}{/block}

{block name='page_content_container'}
    <section id="technologie-detail" class="technologie-detail-page">
        
        {* Breadcrumb navigace *}
        {if isset($breadcrumb) && $breadcrumb}
            <nav class="breadcrumb-nav" aria-label="Breadcrumb">
                <ol class="breadcrumb">
                    {foreach from=$breadcrumb item=crumb}
                        <li class="breadcrumb-item{if $crumb@last} active{/if}">
                            {if !$crumb@last && isset($crumb.url)}
                                <a href="{$crumb.url|escape:'html':'UTF-8'}">{$crumb.title|escape:'html':'UTF-8'}</a>
                            {else}
                                {$crumb.title|escape:'html':'UTF-8'}
                            {/if}
                        </li>
                    {/foreach}
                </ol>
            </nav>
        {/if}

        {* Hero sekce s hlavním obrázkem *}
        <div class="technologie-hero">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="hero-content">
                            <h1 class="hero-title">{$technologie.name|escape:'html':'UTF-8'}</h1>
                            <p class="hero-description">{$technologie.description|escape:'html':'UTF-8'}</p>
                            <div class="hero-actions">
                                <a href="#contact" class="btn btn-primary btn-lg">Získat nabídku</a>
                                <a href="#gallery" class="btn btn-outline-secondary btn-lg">Zobrazit realizace</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="hero-image">
                            {if $technologie.image_path}
                                <img src="{$technologie.image_path|escape:'html':'UTF-8'}" 
                                     alt="{$technologie.name|escape:'html':'UTF-8'}" 
                                     class="img-fluid rounded shadow-lg"
                                     loading="eager">
                            {else}
                                <div class="placeholder-image">
                                    <i class="fas fa-image fa-5x text-muted"></i>
                                    <p class="text-muted mt-3">Obrázek není k dispozici</p>
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {* Detailní popis *}
        {if $technologie.detailed_description}
            <section class="technologie-description">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8 mx-auto">
                            <h2 class="section-title">Podrobný popis technologie</h2>
                            <div class="description-content">
                                {$technologie.detailed_description|nl2br nofilter}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        {/if}

        {* Výhody technologie *}
        {if isset($technologie.advantages_array) && $technologie.advantages_array}
            <section class="technologie-advantages">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-10 mx-auto">
                            <h2 class="section-title">Výhody této technologie</h2>
                            <div class="advantages-grid">
                                {foreach from=$technologie.advantages_array item=advantage}
                                    <div class="advantage-item">
                                        <div class="advantage-icon">
                                            <i class="fas fa-check-circle text-success"></i>
                                        </div>
                                        <div class="advantage-content">
                                            <p>{$advantage|escape:'html':'UTF-8'}</p>
                                        </div>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        {/if}

        {* Oblasti použití *}
        {if isset($technologie.applications_array) && $technologie.applications_array}
            <section class="technologie-applications">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-10 mx-auto">
                            <h2 class="section-title">Oblasti použití</h2>
                            <div class="applications-grid">
                                {foreach from=$technologie.applications_array item=application}
                                    <div class="application-item">
                                        <div class="application-icon">
                                            <i class="fas fa-arrow-right text-primary"></i>
                                        </div>
                                        <div class="application-content">
                                            <p>{$application|escape:'html':'UTF-8'}</p>
                                        </div>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        {/if}

        {* Galerie realizací *}
        {if isset($technologie.gallery_images_array) && $technologie.gallery_images_array}
            <section id="gallery" class="technologie-gallery">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h2 class="section-title">Galerie realizací</h2>
                            <div class="gallery-grid">
                                {foreach from=$technologie.gallery_images_array item=image}
                                    <div class="gallery-item">
                                        <a href="{$image|escape:'html':'UTF-8'}" 
                                           class="gallery-link" 
                                           data-lightbox="technologie-gallery"
                                           data-title="{$technologie.name|escape:'html':'UTF-8'} - Realizace">
                                            <img src="{$image|escape:'html':'UTF-8'}" 
                                                 alt="{$technologie.name|escape:'html':'UTF-8'} - Realizace" 
                                                 class="gallery-image"
                                                 loading="lazy">
                                            <div class="gallery-overlay">
                                                <i class="fas fa-search-plus"></i>
                                            </div>
                                        </a>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        {/if}

        {* Kontaktní sekce *}
        <section id="contact" class="technologie-contact">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <h2 class="section-title">Máte zájem o tuto technologii?</h2>
                        <p class="contact-description">
                            Kontaktujte nás pro nezávaznou konzultaci a cenovou nabídku. 
                            Rádi vám poradíme s výběrem nejvhodnější technologie pro vaše potřeby.
                        </p>
                        <div class="contact-actions">
                            <a href="/kontakt" class="btn btn-primary btn-lg me-3">Kontaktovat nás</a>
                            <a href="tel:+420123456789" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-phone me-2"></i>+420 123 456 789
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        {* Navigace na další technologie *}
        <section class="technologie-navigation">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="navigation-wrapper">
                            <a href="/reklamni-potisk" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Zpět na přehled technologií
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    </section>
{/block}

{block name='page_footer_container'}
    {* Přidání CSS a JS pro detail stránku *}
    <link rel="stylesheet" href="{$module_dir}views/css/front.css">
    <script src="{$module_dir}views/js/front.js"></script>
    
    {* Lightbox pro galerii *}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
{/block}
