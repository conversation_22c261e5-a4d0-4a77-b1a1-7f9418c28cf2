# Bezpečnostní .htaccess pro uploads adresář
# Umístit do modules/technologie/uploads/.htaccess

# Zak<PERSON>zání výpisu obsahu adresáře
Options -Indexes

# <PERSON><PERSON>kov<PERSON><PERSON> spouštění PHP skriptů
<Files "*.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.php3">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.php4">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.php5">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.phtml">
    Order allow,deny
    Deny from all
</Files>

# <PERSON><PERSON><PERSON><PERSON><PERSON> nebezpečných souborů
<FilesMatch "\.(sh|cgi|pl|exe|bat|pif|scr|com|cmd)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Povolení pouze obrázků
<FilesMatch "\.(jpg|jpeg|png|gif|webp|bmp|ico|svg)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# Cache pro obrázky (1 rok)
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
</IfModule>

# Gzip komprese pro obrázky (pokud je možná)
<IfModule mod_deflate.c>
    <FilesMatch "\.(svg)$">
        SetOutputFilter DEFLATE
    </FilesMatch>
</IfModule>